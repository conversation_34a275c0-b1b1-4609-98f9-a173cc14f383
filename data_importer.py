"""
裁判文书数据导入模块
"""
import pandas as pd
import pymongo
from pymongo import MongoClient
from datetime import datetime, timedelta
import logging
import os
import uuid
import time
from typing import Dict, List, Optional, Tuple
import threading
from dataclasses import dataclass

@dataclass
class ImportProgress:
    """导入进度数据类"""
    total_files: int = 0
    processed_files: int = 0
    current_file: str = ""
    total_records: int = 0
    processed_records: int = 0
    success_records: int = 0
    failed_records: int = 0
    start_time: datetime = None
    current_speed: float = 0.0  # 记录/秒
    estimated_remaining: str = "计算中..."
    status: str = "待开始"  # 待开始, 进行中, 已完成, 已暂停, 出错
    error_message: str = ""

class JudgmentDataImporter:
    """裁判文书数据导入器"""
    
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judgment_documents"):
        self.client = MongoClient(connection_string)
        self.db = self.client[db_name]
        self.collection = self.db.documents
        self.progress = ImportProgress()
        self.is_running = False
        self.should_stop = False
        self.batch_size = 1000  # 批量插入大小
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('import.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def clean_and_convert_data(self, df: pd.DataFrame) -> List[Dict]:
        """清洗和转换数据"""
        documents = []
        
        for _, row in df.iterrows():
            try:
                # 数据清洗和转换
                doc = {
                    "original_url": str(row["原始链接"]).strip(),
                    "case_number": str(row["案号"]).strip(),
                    "case_name": str(row["案件名称"]).strip(),
                    "court": str(row["法院"]).strip(),
                    "region": str(row["所属地区"]).strip(),
                    "case_type": str(row["案件类型"]).strip(),
                    "case_type_code": int(row["案件类型编码"]) if pd.notna(row["案件类型编码"]) else 0,
                    "source": str(row["来源"]).strip(),
                    "trial_procedure": str(row["审理程序"]).strip(),
                    "parties": str(row["当事人"]).strip(),
                    "case_cause": str(row["案由"]).strip(),
                }
                
                # 处理日期字段
                try:
                    doc["judgment_date"] = pd.to_datetime(row["裁判日期"]).to_pydatetime()
                except:
                    doc["judgment_date"] = datetime.now()
                
                try:
                    doc["publish_date"] = pd.to_datetime(row["公开日期"]).to_pydatetime()
                except:
                    doc["publish_date"] = datetime.now()
                
                # 处理可选字段
                doc["legal_basis"] = str(row["法律依据"]).strip() if pd.notna(row["法律依据"]) else None
                doc["full_text"] = str(row["全文"]).strip() if pd.notna(row["全文"]) else None
                
                # 添加导入信息
                doc["import_info"] = {
                    "import_time": datetime.now(),
                    "source_file": self.progress.current_file,
                    "batch_id": str(uuid.uuid4())
                }
                
                documents.append(doc)
                
            except Exception as e:
                self.logger.error(f"处理记录时出错: {e}, 行数据: {row.to_dict()}")
                self.progress.failed_records += 1
                continue
        
        return documents
    
    def import_single_file(self, file_path: str) -> Tuple[int, int]:
        """导入单个文件"""
        self.progress.current_file = os.path.basename(file_path)
        self.logger.info(f"开始导入文件: {file_path}")
        
        success_count = 0
        error_count = 0
        
        try:
            # 分块读取大文件
            chunk_size = self.batch_size
            for chunk_num, chunk in enumerate(pd.read_csv(file_path, chunksize=chunk_size)):
                if self.should_stop:
                    self.logger.info("收到停止信号，中断导入")
                    break
                
                # 清洗数据
                documents = self.clean_and_convert_data(chunk)
                
                if documents:
                    try:
                        # 批量插入
                        result = self.collection.insert_many(documents, ordered=False)
                        success_count += len(result.inserted_ids)
                        self.progress.success_records += len(result.inserted_ids)
                        
                    except pymongo.errors.BulkWriteError as e:
                        # 处理批量写入错误（如重复键）
                        success_count += e.details.get('nInserted', 0)
                        error_count += len(e.details.get('writeErrors', []))
                        self.progress.success_records += e.details.get('nInserted', 0)
                        self.progress.failed_records += len(e.details.get('writeErrors', []))
                        
                        for error in e.details.get('writeErrors', []):
                            self.logger.warning(f"插入错误: {error.get('errmsg', 'Unknown error')}")
                
                # 更新进度
                self.progress.processed_records += len(chunk)
                
                # 计算速度
                if self.progress.start_time:
                    elapsed = (datetime.now() - self.progress.start_time).total_seconds()
                    if elapsed > 0:
                        self.progress.current_speed = self.progress.processed_records / elapsed
                        
                        # 估算剩余时间
                        remaining_records = self.progress.total_records - self.progress.processed_records
                        if self.progress.current_speed > 0:
                            remaining_seconds = remaining_records / self.progress.current_speed
                            self.progress.estimated_remaining = str(timedelta(seconds=int(remaining_seconds)))
                
                self.logger.info(f"已处理 {self.progress.processed_records}/{self.progress.total_records} 条记录")
                
        except Exception as e:
            self.logger.error(f"导入文件 {file_path} 时出错: {e}")
            self.progress.error_message = str(e)
            error_count += 1
        
        return success_count, error_count
    
    def get_all_csv_files(self, base_path: str = "裁判文书") -> List[str]:
        """获取所有CSV文件路径"""
        csv_files = []
        
        for year in ["2021年", "2022年", "2023年", "2024年"]:
            year_path = os.path.join(base_path, year)
            if os.path.exists(year_path):
                for file in os.listdir(year_path):
                    if file.endswith('.csv'):
                        csv_files.append(os.path.join(year_path, file))
        
        return sorted(csv_files)
    
    def estimate_total_records(self, csv_files: List[str]) -> int:
        """估算总记录数"""
        total = 0
        for file_path in csv_files:
            try:
                # 快速计算行数
                with open(file_path, 'r', encoding='utf-8') as f:
                    total += sum(1 for _ in f) - 1  # 减去标题行
            except Exception as e:
                self.logger.warning(f"无法计算文件 {file_path} 的行数: {e}")
        
        return total
    
    def start_import(self, base_path: str = "裁判文书"):
        """开始导入过程"""
        if self.is_running:
            self.logger.warning("导入已在进行中")
            return

        print("开始导入过程...")  # 调试信息
        self.is_running = True
        self.should_stop = False
        self.progress = ImportProgress()
        self.progress.start_time = datetime.now()
        self.progress.status = "进行中"

        try:
            # 获取所有CSV文件
            print("正在获取CSV文件列表...")
            csv_files = self.get_all_csv_files(base_path)
            self.progress.total_files = len(csv_files)

            if not csv_files:
                self.logger.error("未找到CSV文件")
                self.progress.status = "出错"
                self.progress.error_message = "未找到CSV文件"
                return

            self.logger.info(f"找到 {len(csv_files)} 个CSV文件")
            print(f"找到 {len(csv_files)} 个CSV文件")

            # 快速估算总记录数（只检查前几个文件）
            print("正在估算总记录数...")
            self.progress.total_records = self.estimate_total_records(csv_files[:5])  # 只估算前5个文件
            if len(csv_files) > 5:
                # 根据前5个文件的平均值估算总数
                avg_records = self.progress.total_records / 5
                self.progress.total_records = int(avg_records * len(csv_files))

            self.logger.info(f"预估总记录数: {self.progress.total_records:,}")
            print(f"预估总记录数: {self.progress.total_records:,}")

            # 逐个导入文件
            for i, file_path in enumerate(csv_files):
                if self.should_stop:
                    break

                print(f"正在处理文件 {i+1}/{len(csv_files)}: {file_path}")
                success, errors = self.import_single_file(file_path)
                self.progress.processed_files += 1

                self.logger.info(f"文件 {file_path} 导入完成: 成功 {success}, 失败 {errors}")

            # 完成导入
            if not self.should_stop:
                self.progress.status = "已完成"
                self.logger.info("所有文件导入完成")
                print("所有文件导入完成")
            else:
                self.progress.status = "已暂停"
                self.logger.info("导入被用户暂停")
                print("导入被用户暂停")

        except Exception as e:
            self.logger.error(f"导入过程出错: {e}")
            self.progress.status = "出错"
            self.progress.error_message = str(e)
            print(f"导入过程出错: {e}")

        finally:
            self.is_running = False
            print("导入过程结束")
    
    def stop_import(self):
        """停止导入"""
        self.should_stop = True
        self.logger.info("正在停止导入...")
    
    def get_progress(self) -> Dict:
        """获取当前进度"""
        return {
            "total_files": self.progress.total_files,
            "processed_files": self.progress.processed_files,
            "current_file": self.progress.current_file,
            "total_records": self.progress.total_records,
            "processed_records": self.progress.processed_records,
            "success_records": self.progress.success_records,
            "failed_records": self.progress.failed_records,
            "current_speed": round(self.progress.current_speed, 2),
            "estimated_remaining": self.progress.estimated_remaining,
            "status": self.progress.status,
            "error_message": self.progress.error_message,
            "progress_percentage": round((self.progress.processed_records / max(self.progress.total_records, 1)) * 100, 2)
        }

def main():
    """测试函数"""
    importer = JudgmentDataImporter()
    
    # 在新线程中启动导入
    import_thread = threading.Thread(target=importer.start_import)
    import_thread.start()
    
    # 监控进度
    while import_thread.is_alive():
        progress = importer.get_progress()
        print(f"进度: {progress['progress_percentage']:.1f}% - {progress['status']}")
        time.sleep(5)
    
    print("导入完成")

if __name__ == "__main__":
    main()
