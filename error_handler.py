"""
错误处理和日志管理模块
"""
import logging
import os
import traceback
from datetime import datetime
from typing import Dict, List, Optional
import json
from dataclasses import dataclass, asdict
from enum import Enum

class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

@dataclass
class ErrorRecord:
    """错误记录数据类"""
    timestamp: datetime
    level: str
    message: str
    file_path: Optional[str] = None
    line_number: Optional[int] = None
    exception_type: Optional[str] = None
    stack_trace: Optional[str] = None
    context: Optional[Dict] = None

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.error_records: List[ErrorRecord] = []
        self.max_records = 1000  # 最大保存记录数
        
        # 创建日志目录
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 设置日志配置
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志配置"""
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 主日志文件
        main_log_file = os.path.join(self.log_dir, 'main.log')
        main_handler = logging.FileHandler(main_log_file, encoding='utf-8')
        main_handler.setFormatter(formatter)
        main_handler.setLevel(logging.INFO)
        
        # 错误日志文件
        error_log_file = os.path.join(self.log_dir, 'error.log')
        error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        
        # 控制台输出
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        root_logger.addHandler(main_handler)
        root_logger.addHandler(error_handler)
        root_logger.addHandler(console_handler)
        
        # 创建专用日志器
        self.logger = logging.getLogger('JudgmentImporter')
    
    def log_error(self, message: str, exception: Optional[Exception] = None, 
                  context: Optional[Dict] = None, file_path: Optional[str] = None):
        """记录错误"""
        error_record = ErrorRecord(
            timestamp=datetime.now(),
            level=LogLevel.ERROR.value,
            message=message,
            file_path=file_path,
            context=context
        )
        
        if exception:
            error_record.exception_type = type(exception).__name__
            error_record.stack_trace = traceback.format_exc()
        
        # 添加到记录列表
        self.error_records.append(error_record)
        
        # 保持记录数量限制
        if len(self.error_records) > self.max_records:
            self.error_records = self.error_records[-self.max_records:]
        
        # 写入日志
        self.logger.error(f"{message} | Context: {context} | Exception: {exception}")
        
        # 保存到文件
        self.save_error_record(error_record)
    
    def log_warning(self, message: str, context: Optional[Dict] = None):
        """记录警告"""
        error_record = ErrorRecord(
            timestamp=datetime.now(),
            level=LogLevel.WARNING.value,
            message=message,
            context=context
        )
        
        self.error_records.append(error_record)
        self.logger.warning(f"{message} | Context: {context}")
    
    def log_info(self, message: str, context: Optional[Dict] = None):
        """记录信息"""
        error_record = ErrorRecord(
            timestamp=datetime.now(),
            level=LogLevel.INFO.value,
            message=message,
            context=context
        )
        
        self.error_records.append(error_record)
        self.logger.info(f"{message} | Context: {context}")
    
    def save_error_record(self, record: ErrorRecord):
        """保存错误记录到JSON文件"""
        error_file = os.path.join(self.log_dir, 'errors.json')
        
        try:
            # 读取现有记录
            if os.path.exists(error_file):
                with open(error_file, 'r', encoding='utf-8') as f:
                    records = json.load(f)
            else:
                records = []
            
            # 添加新记录
            record_dict = asdict(record)
            record_dict['timestamp'] = record.timestamp.isoformat()
            records.append(record_dict)
            
            # 保持记录数量限制
            if len(records) > self.max_records:
                records = records[-self.max_records:]
            
            # 写入文件
            with open(error_file, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.critical(f"无法保存错误记录: {e}")
    
    def get_recent_errors(self, limit: int = 50) -> List[Dict]:
        """获取最近的错误记录"""
        recent_records = self.error_records[-limit:] if self.error_records else []
        return [asdict(record) for record in recent_records]
    
    def get_error_summary(self) -> Dict:
        """获取错误统计摘要"""
        if not self.error_records:
            return {
                'total_errors': 0,
                'error_types': {},
                'recent_24h': 0
            }
        
        # 统计错误类型
        error_types = {}
        recent_24h = 0
        now = datetime.now()
        
        for record in self.error_records:
            # 统计错误类型
            level = record.level
            error_types[level] = error_types.get(level, 0) + 1
            
            # 统计最近24小时的错误
            if (now - record.timestamp).total_seconds() < 86400:  # 24小时
                recent_24h += 1
        
        return {
            'total_errors': len(self.error_records),
            'error_types': error_types,
            'recent_24h': recent_24h
        }
    
    def clear_old_logs(self, days: int = 7):
        """清理旧日志文件"""
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
            
            for filename in os.listdir(self.log_dir):
                file_path = os.path.join(self.log_dir, filename)
                if os.path.isfile(file_path):
                    file_time = os.path.getmtime(file_path)
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        self.logger.info(f"已删除旧日志文件: {filename}")
                        
        except Exception as e:
            self.logger.error(f"清理旧日志文件时出错: {e}")

class DataValidationError(Exception):
    """数据验证错误"""
    pass

class DatabaseConnectionError(Exception):
    """数据库连接错误"""
    pass

class FileProcessingError(Exception):
    """文件处理错误"""
    pass

def handle_exceptions(error_handler: ErrorHandler):
    """异常处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except DataValidationError as e:
                error_handler.log_error(f"数据验证错误: {str(e)}", e, {"function": func.__name__})
                raise
            except DatabaseConnectionError as e:
                error_handler.log_error(f"数据库连接错误: {str(e)}", e, {"function": func.__name__})
                raise
            except FileProcessingError as e:
                error_handler.log_error(f"文件处理错误: {str(e)}", e, {"function": func.__name__})
                raise
            except Exception as e:
                error_handler.log_error(f"未知错误: {str(e)}", e, {"function": func.__name__})
                raise
        return wrapper
    return decorator

# 全局错误处理器实例
global_error_handler = ErrorHandler()

def test_error_handler():
    """测试错误处理器"""
    handler = ErrorHandler("test_logs")
    
    # 测试不同类型的日志
    handler.log_info("系统启动", {"version": "1.0"})
    handler.log_warning("内存使用率较高", {"memory_usage": "85%"})
    
    try:
        raise ValueError("测试异常")
    except Exception as e:
        handler.log_error("测试错误记录", e, {"test": True})
    
    # 获取错误摘要
    summary = handler.get_error_summary()
    print("错误摘要:", summary)
    
    # 获取最近错误
    recent = handler.get_recent_errors(10)
    print(f"最近错误数量: {len(recent)}")

if __name__ == "__main__":
    test_error_handler()
