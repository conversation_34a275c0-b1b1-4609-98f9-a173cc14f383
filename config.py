"""
系统配置文件
"""
import os
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class DatabaseConfig:
    """数据库配置"""
    connection_string: str = "mongodb://localhost:27017/"
    database_name: str = "judgment_documents"
    collection_name: str = "documents"
    connection_timeout: int = 30000  # 毫秒
    server_selection_timeout: int = 30000  # 毫秒
    max_pool_size: int = 100
    min_pool_size: int = 10

@dataclass
class ImportConfig:
    """导入配置"""
    batch_size: int = 1000  # 批量插入大小
    chunk_size: int = 1000  # CSV读取块大小
    max_workers: int = 4  # 最大工作线程数
    retry_attempts: int = 3  # 重试次数
    retry_delay: int = 5  # 重试延迟（秒）
    data_path: str = "裁判文书"  # 数据文件路径
    supported_years: List[str] = None  # 支持的年份
    
    def __post_init__(self):
        if self.supported_years is None:
            self.supported_years = ["2021年", "2022年", "2023年", "2024年"]

@dataclass
class WebConfig:
    """Web服务配置"""
    host: str = "0.0.0.0"
    port: int = 5000
    debug: bool = False
    secret_key: str = "judgment_documents_secret_key_2024"
    max_content_length: int = 16 * 1024 * 1024  # 16MB

@dataclass
class LogConfig:
    """日志配置"""
    log_dir: str = "logs"
    log_level: str = "INFO"
    max_log_files: int = 10
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    log_retention_days: int = 30

@dataclass
class SystemConfig:
    """系统总配置"""
    database: DatabaseConfig
    import_config: ImportConfig
    web: WebConfig
    logging: LogConfig
    
    # 性能配置
    memory_limit_mb: int = 2048  # 内存限制
    cpu_cores: int = 4  # CPU核心数
    
    # 安全配置
    enable_auth: bool = False
    admin_username: str = "admin"
    admin_password: str = "admin123"

def load_config() -> SystemConfig:
    """加载配置"""
    # 从环境变量读取配置（如果存在）
    db_config = DatabaseConfig(
        connection_string=os.getenv("MONGODB_URI", "mongodb://localhost:27017/"),
        database_name=os.getenv("DB_NAME", "judgment_documents")
    )
    
    import_config = ImportConfig(
        batch_size=int(os.getenv("BATCH_SIZE", "1000")),
        chunk_size=int(os.getenv("CHUNK_SIZE", "1000")),
        data_path=os.getenv("DATA_PATH", "裁判文书")
    )
    
    web_config = WebConfig(
        host=os.getenv("WEB_HOST", "0.0.0.0"),
        port=int(os.getenv("WEB_PORT", "5000")),
        debug=os.getenv("DEBUG", "False").lower() == "true"
    )
    
    log_config = LogConfig(
        log_dir=os.getenv("LOG_DIR", "logs"),
        log_level=os.getenv("LOG_LEVEL", "INFO")
    )
    
    return SystemConfig(
        database=db_config,
        import_config=import_config,
        web=web_config,
        logging=log_config
    )

# 全局配置实例
CONFIG = load_config()

def print_config():
    """打印当前配置"""
    print("=== 系统配置 ===")
    print(f"数据库连接: {CONFIG.database.connection_string}")
    print(f"数据库名称: {CONFIG.database.database_name}")
    print(f"批量大小: {CONFIG.import_config.batch_size}")
    print(f"数据路径: {CONFIG.import_config.data_path}")
    print(f"Web服务: http://{CONFIG.web.host}:{CONFIG.web.port}")
    print(f"日志目录: {CONFIG.logging.log_dir}")
    print("================")

if __name__ == "__main__":
    print_config()
