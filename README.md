# 裁判文书导入系统

一个用于批量导入2021-2024年裁判文书数据到MongoDB的Web可视化系统。

## 功能特性

- 🏛️ **批量数据导入**: 支持CSV格式的裁判文书数据批量导入
- 📊 **实时监控**: Web界面实时显示导入进度、处理速度和状态
- 🗄️ **MongoDB存储**: 优化的数据库结构设计，支持高效查询
- 🔍 **索引优化**: 自动创建索引，支持快速检索
- 📈 **可视化面板**: 美观的Web界面，支持实时图表展示
- 🛡️ **错误处理**: 完善的错误处理和日志记录机制
- ⚡ **高性能**: 支持大数据量处理，批量插入优化

## 系统要求

- Python 3.8+
- MongoDB 4.0+
- 内存: 建议4GB以上
- 磁盘空间: 根据数据量确定（建议预留数据量的2-3倍空间）

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd judgment-documents-importer
```

### 2. 创建虚拟环境
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 启动MongoDB
确保MongoDB服务正在运行：
```bash
# Windows (如果安装为服务)
net start MongoDB

# Linux/Mac
sudo systemctl start mongod
# 或
mongod --dbpath /path/to/your/db
```

### 5. 准备数据文件
将裁判文书CSV文件按以下结构组织：
```
裁判文书/
├── 2021年/
│   ├── 2021年01月裁判文书数据.csv
│   ├── 2021年02月裁判文书数据.csv
│   └── ...
├── 2022年/
│   ├── 2022年01月裁判文书数据.csv
│   └── ...
├── 2023年/
└── 2024年/
```

## 使用方法

### 快速启动
```bash
python start.py
```

启动脚本会自动：
- 检查系统依赖
- 验证MongoDB连接
- 检查数据文件
- 启动Web服务器
- 打开浏览器

### 手动启动
```bash
# 初始化数据库
python database_design.py

# 启动Web服务
python web_app.py
```

### Web界面操作

1. **访问系统**: 打开浏览器访问 `http://localhost:5000`
2. **初始化数据库**: 点击"初始化数据库"按钮
3. **开始导入**: 点击"开始导入"按钮启动数据导入
4. **监控进度**: 实时查看导入进度和统计信息
5. **停止导入**: 如需要可点击"停止导入"按钮

## 数据结构

### CSV文件字段
系统支持以下15个字段的CSV文件：
1. 原始链接
2. 案号
3. 案件名称
4. 法院
5. 所属地区
6. 案件类型
7. 案件类型编码
8. 来源
9. 审理程序
10. 裁判日期
11. 公开日期
12. 当事人
13. 案由
14. 法律依据
15. 全文

### MongoDB文档结构
```json
{
  "_id": "ObjectId",
  "original_url": "string",
  "case_number": "string",
  "case_name": "string",
  "court": "string",
  "region": "string",
  "case_type": "string",
  "case_type_code": "int",
  "source": "string",
  "trial_procedure": "string",
  "judgment_date": "date",
  "publish_date": "date",
  "parties": "string",
  "case_cause": "string",
  "legal_basis": "string",
  "full_text": "string",
  "import_info": {
    "import_time": "date",
    "source_file": "string",
    "batch_id": "string"
  }
}
```

## 配置说明

### 环境变量
可通过环境变量自定义配置：
```bash
export MONGODB_URI="mongodb://localhost:27017/"
export DB_NAME="judgment_documents"
export BATCH_SIZE="1000"
export WEB_PORT="5000"
export LOG_LEVEL="INFO"
```

### 配置文件
修改 `config.py` 中的配置类：
- `DatabaseConfig`: 数据库连接配置
- `ImportConfig`: 导入参数配置
- `WebConfig`: Web服务配置
- `LogConfig`: 日志配置

## 性能优化

### 导入性能
- 批量大小: 默认1000条记录/批次
- 并发处理: 支持多线程处理
- 内存管理: 分块读取大文件
- 错误恢复: 支持断点续传

### 数据库优化
- 索引策略: 自动创建常用查询索引
- 唯一约束: 案号唯一性保证
- 文本搜索: 支持全文检索
- 复合索引: 优化复杂查询

## 监控和日志

### 实时监控
- 导入进度百分比
- 处理速度（条/秒）
- 成功/失败统计
- 预计剩余时间
- 当前处理文件

### 日志系统
- 主日志: `logs/main.log`
- 错误日志: `logs/error.log`
- 错误记录: `logs/errors.json`
- 自动清理: 支持日志轮转

## 故障排除

### 常见问题

1. **MongoDB连接失败**
   - 检查MongoDB服务是否启动
   - 验证连接字符串是否正确
   - 检查防火墙设置

2. **内存不足**
   - 减小批量大小 (`BATCH_SIZE`)
   - 减小读取块大小 (`CHUNK_SIZE`)
   - 增加系统内存

3. **导入速度慢**
   - 增加批量大小
   - 检查磁盘I/O性能
   - 优化MongoDB配置

4. **数据重复**
   - 系统会自动跳过重复的案号
   - 检查错误日志了解详情

### 日志查看
```bash
# 查看主日志
tail -f logs/main.log

# 查看错误日志
tail -f logs/error.log

# 查看错误统计
python -c "from error_handler import global_error_handler; print(global_error_handler.get_error_summary())"
```

## 开发说明

### 项目结构
```
├── start.py              # 启动脚本
├── config.py             # 配置管理
├── database_design.py    # 数据库设计
├── data_importer.py      # 数据导入器
├── web_app.py           # Web应用
├── error_handler.py     # 错误处理
├── requirements.txt     # 依赖列表
├── templates/           # HTML模板
│   └── index.html
└── logs/               # 日志目录
```

### 扩展开发
- 添加新的数据源格式支持
- 实现数据导出功能
- 添加用户认证系统
- 集成更多可视化图表

## 许可证

MIT License

## 支持

如有问题请提交Issue或联系开发团队。
