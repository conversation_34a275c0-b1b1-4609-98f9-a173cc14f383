#!/usr/bin/env python3
"""
裁判文书导入系统快速启动脚本（优化版）
"""
import os
import sys
import time
import webbrowser
from threading import Timer
from config import CONFIG

def quick_check():
    """快速检查系统状态"""
    print("🚀 快速启动裁判文书导入系统")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    # 检查关键包
    try:
        import flask, flask_socketio, pymongo, pandas
        print("✅ 核心依赖包检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请运行: pip install flask flask-socketio pymongo pandas")
        return False
    
    # 检查数据目录
    if not os.path.exists(CONFIG.import_config.data_path):
        print(f"❌ 数据目录不存在: {CONFIG.import_config.data_path}")
        return False
    
    print("✅ 数据目录存在")
    return True

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(2)
    url = f"http://localhost:{CONFIG.web.port}"
    print(f"🌐 正在打开浏览器: {url}")
    webbrowser.open(url)

def main():
    """主函数"""
    if not quick_check():
        input("按回车键退出...")
        sys.exit(1)
    
    # 创建必要目录
    os.makedirs(CONFIG.logging.log_dir, exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    print("\n" + "=" * 50)
    print("🎯 启动Web服务器...")
    print(f"📱 访问地址: http://localhost:{CONFIG.web.port}")
    print("💡 提示: 启动后先点击'初始化数据库'，再点击'开始导入'")
    print("⚠️  如遇到连接问题，请刷新页面")
    print("\n按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    # 延迟打开浏览器
    Timer(2.0, open_browser_delayed).start()
    
    try:
        from web_app import app, socketio
        
        # 使用优化的配置启动
        socketio.run(
            app,
            host=CONFIG.web.host,
            port=CONFIG.web.port,
            debug=False,  # 关闭调试模式提高性能
            use_reloader=False,  # 关闭自动重载
            log_output=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
