"""
裁判文书导入Web可视化面板
"""
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import threading
import time
import json
from datetime import datetime
from data_importer import JudgmentDataImporter
from database_design import JudgmentDocumentDB

app = Flask(__name__)
app.config['SECRET_KEY'] = 'judgment_documents_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局导入器实例
importer = JudgmentDataImporter()
db_manager = JudgmentDocumentDB()

# 进度监控线程
progress_thread = None
progress_thread_running = False

def progress_monitor():
    """进度监控线程函数"""
    global progress_thread_running
    progress_thread_running = True
    
    while progress_thread_running:
        if importer.is_running:
            progress_data = importer.get_progress()
            socketio.emit('progress_update', progress_data)
        time.sleep(1)  # 每秒更新一次

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取当前状态"""
    progress = importer.get_progress()
    db_stats = db_manager.get_collection_stats()
    
    return jsonify({
        'import_status': progress,
        'database_stats': db_stats,
        'is_running': importer.is_running
    })

@app.route('/api/start_import', methods=['POST'])
def start_import():
    """启动导入"""
    if importer.is_running:
        return jsonify({'success': False, 'message': '导入已在进行中'})
    
    try:
        # 在新线程中启动导入
        import_thread = threading.Thread(target=importer.start_import)
        import_thread.daemon = True
        import_thread.start()
        
        # 启动进度监控
        global progress_thread, progress_thread_running
        if not progress_thread_running:
            progress_thread = threading.Thread(target=progress_monitor)
            progress_thread.daemon = True
            progress_thread.start()
        
        return jsonify({'success': True, 'message': '导入已启动'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'启动失败: {str(e)}'})

@app.route('/api/stop_import', methods=['POST'])
def stop_import():
    """停止导入"""
    try:
        importer.stop_import()
        return jsonify({'success': True, 'message': '正在停止导入...'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'停止失败: {str(e)}'})

@app.route('/api/init_database', methods=['POST'])
def init_database():
    """初始化数据库"""
    try:
        db_manager.setup_database()
        return jsonify({'success': True, 'message': '数据库初始化成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'初始化失败: {str(e)}'})

@app.route('/api/files')
def get_files():
    """获取文件列表"""
    try:
        csv_files = importer.get_all_csv_files()
        file_info = []
        
        for file_path in csv_files:
            try:
                import os
                stat = os.stat(file_path)
                file_info.append({
                    'path': file_path,
                    'name': os.path.basename(file_path),
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
            except:
                continue
        
        return jsonify({'success': True, 'files': file_info})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取文件列表失败: {str(e)}'})

@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    print('客户端已连接')
    # 发送当前状态
    progress = importer.get_progress()
    emit('progress_update', progress)

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    print('客户端已断开连接')

if __name__ == '__main__':
    # 创建模板目录
    import os
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("启动Web服务器...")
    print("访问地址: http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
