"""
裁判文书MongoDB数据库设计
"""
from pymongo import MongoClient, ASCENDING, DESCENDING, TEXT
from datetime import datetime
import logging

class JudgmentDocumentDB:
    """裁判文书数据库设计类"""
    
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judgment_documents"):
        self.client = MongoClient(connection_string)
        self.db = self.client[db_name]
        self.collection = self.db.documents
        
    def create_schema_validation(self):
        """创建数据验证规则"""
        schema = {
            "$jsonSchema": {
                "bsonType": "object",
                "required": [
                    "original_url", "case_number", "case_name", "court", 
                    "region", "case_type", "case_type_code", "source",
                    "trial_procedure", "judgment_date", "publish_date",
                    "parties", "case_cause"
                ],
                "properties": {
                    "_id": {"bsonType": "objectId"},
                    "original_url": {
                        "bsonType": "string",
                        "description": "原始链接，必须是字符串"
                    },
                    "case_number": {
                        "bsonType": "string",
                        "description": "案号，必须是字符串"
                    },
                    "case_name": {
                        "bsonType": "string",
                        "description": "案件名称，必须是字符串"
                    },
                    "court": {
                        "bsonType": "string",
                        "description": "法院，必须是字符串"
                    },
                    "region": {
                        "bsonType": "string",
                        "description": "所属地区，必须是字符串"
                    },
                    "case_type": {
                        "bsonType": "string",
                        "description": "案件类型，必须是字符串"
                    },
                    "case_type_code": {
                        "bsonType": "int",
                        "description": "案件类型编码，必须是整数"
                    },
                    "source": {
                        "bsonType": "string",
                        "description": "来源，必须是字符串"
                    },
                    "trial_procedure": {
                        "bsonType": "string",
                        "description": "审理程序，必须是字符串"
                    },
                    "judgment_date": {
                        "bsonType": "date",
                        "description": "裁判日期，必须是日期类型"
                    },
                    "publish_date": {
                        "bsonType": "date",
                        "description": "公开日期，必须是日期类型"
                    },
                    "parties": {
                        "bsonType": "string",
                        "description": "当事人，必须是字符串"
                    },
                    "case_cause": {
                        "bsonType": "string",
                        "description": "案由，必须是字符串"
                    },
                    "legal_basis": {
                        "bsonType": ["string", "null"],
                        "description": "法律依据，可以为空"
                    },
                    "full_text": {
                        "bsonType": ["string", "null"],
                        "description": "全文，可以为空"
                    },
                    "import_info": {
                        "bsonType": "object",
                        "properties": {
                            "import_time": {"bsonType": "date"},
                            "source_file": {"bsonType": "string"},
                            "batch_id": {"bsonType": "string"}
                        }
                    }
                }
            }
        }
        
        try:
            # 删除现有集合（如果存在）
            self.db.drop_collection("documents")
            # 创建带验证规则的集合
            self.db.create_collection("documents", validator=schema)
            print("✓ 数据验证规则创建成功")
        except Exception as e:
            print(f"✗ 创建数据验证规则失败: {e}")
    
    def create_indexes(self):
        """创建索引以优化查询性能"""
        indexes = [
            # 单字段索引
            ("case_number", ASCENDING),  # 案号索引（唯一）
            ("court", ASCENDING),        # 法院索引
            ("region", ASCENDING),       # 地区索引
            ("case_type", ASCENDING),    # 案件类型索引
            ("case_cause", ASCENDING),   # 案由索引
            ("judgment_date", DESCENDING), # 裁判日期索引（降序，最新的在前）
            ("publish_date", DESCENDING),  # 公开日期索引
            
            # 复合索引
            ([("region", ASCENDING), ("court", ASCENDING)], {}),  # 地区+法院
            ([("case_type", ASCENDING), ("judgment_date", DESCENDING)], {}),  # 类型+日期
            ([("court", ASCENDING), ("judgment_date", DESCENDING)], {}),  # 法院+日期
            
            # 文本索引（用于全文搜索）
            ([("case_name", TEXT), ("parties", TEXT), ("case_cause", TEXT)], {}),
        ]
        
        try:
            # 创建唯一索引
            self.collection.create_index("case_number", unique=True)
            print("✓ 案号唯一索引创建成功")
            
            # 创建其他索引
            for index_spec, options in indexes:
                if isinstance(index_spec, str):
                    self.collection.create_index(index_spec)
                else:
                    self.collection.create_index(index_spec, **options)
            
            print("✓ 所有索引创建成功")
            
            # 显示索引信息
            indexes_info = self.collection.list_indexes()
            print("\n当前索引列表:")
            for idx in indexes_info:
                print(f"  - {idx['name']}: {idx.get('key', {})}")
                
        except Exception as e:
            print(f"✗ 创建索引失败: {e}")
    
    def get_collection_stats(self):
        """获取集合统计信息"""
        try:
            stats = self.db.command("collStats", "documents")
            return {
                "document_count": stats.get("count", 0),
                "storage_size": stats.get("storageSize", 0),
                "index_count": stats.get("nindexes", 0),
                "total_index_size": stats.get("totalIndexSize", 0)
            }
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}
    
    def setup_database(self):
        """初始化数据库"""
        print("=== 初始化裁判文书数据库 ===")
        print(f"数据库: {self.db.name}")
        print(f"集合: {self.collection.name}")
        
        # 创建验证规则
        self.create_schema_validation()
        
        # 创建索引
        self.create_indexes()
        
        print("\n✓ 数据库初始化完成")

def main():
    """主函数"""
    db = JudgmentDocumentDB()
    db.setup_database()

if __name__ == "__main__":
    main()
