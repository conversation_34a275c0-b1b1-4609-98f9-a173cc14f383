"""
裁判文书MongoDB数据库设计
"""
from pymongo import MongoClient, ASCENDING, DESCENDING, TEXT
from datetime import datetime
import logging

class JudgmentDocumentDB:
    """裁判文书数据库设计类"""
    
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judgment_documents"):
        self.client = MongoClient(connection_string)
        self.db = self.client[db_name]
        self.collection = self.db.documents
        
    def create_schema_validation(self):
        """创建数据验证规则"""
        schema = {
            "$jsonSchema": {
                "bsonType": "object",
                "required": [
                    "original_url", "case_number", "case_name", "court", 
                    "region", "case_type", "case_type_code", "source",
                    "trial_procedure", "judgment_date", "publish_date",
                    "parties", "case_cause"
                ],
                "properties": {
                    "_id": {"bsonType": "objectId"},
                    "original_url": {
                        "bsonType": "string",
                        "description": "原始链接，必须是字符串"
                    },
                    "case_number": {
                        "bsonType": "string",
                        "description": "案号，必须是字符串"
                    },
                    "case_name": {
                        "bsonType": "string",
                        "description": "案件名称，必须是字符串"
                    },
                    "court": {
                        "bsonType": "string",
                        "description": "法院，必须是字符串"
                    },
                    "region": {
                        "bsonType": "string",
                        "description": "所属地区，必须是字符串"
                    },
                    "case_type": {
                        "bsonType": "string",
                        "description": "案件类型，必须是字符串"
                    },
                    "case_type_code": {
                        "bsonType": "int",
                        "description": "案件类型编码，必须是整数"
                    },
                    "source": {
                        "bsonType": "string",
                        "description": "来源，必须是字符串"
                    },
                    "trial_procedure": {
                        "bsonType": "string",
                        "description": "审理程序，必须是字符串"
                    },
                    "judgment_date": {
                        "bsonType": "date",
                        "description": "裁判日期，必须是日期类型"
                    },
                    "publish_date": {
                        "bsonType": "date",
                        "description": "公开日期，必须是日期类型"
                    },
                    "parties": {
                        "bsonType": "string",
                        "description": "当事人，必须是字符串"
                    },
                    "case_cause": {
                        "bsonType": "string",
                        "description": "案由，必须是字符串"
                    },
                    "legal_basis": {
                        "bsonType": ["string", "null"],
                        "description": "法律依据，可以为空"
                    },
                    "full_text": {
                        "bsonType": ["string", "null"],
                        "description": "全文，可以为空"
                    },
                    "import_info": {
                        "bsonType": "object",
                        "properties": {
                            "import_time": {"bsonType": "date"},
                            "source_file": {"bsonType": "string"},
                            "batch_id": {"bsonType": "string"}
                        }
                    }
                }
            }
        }
        
        try:
            # 删除现有集合（如果存在）
            self.db.drop_collection("documents")
            # 创建带验证规则的集合
            self.db.create_collection("documents", validator=schema)
            print("✓ 数据验证规则创建成功")
        except Exception as e:
            print(f"✗ 创建数据验证规则失败: {e}")
    
    def create_indexes(self):
        """创建索引以优化查询性能"""
        try:
            # 获取现有索引
            existing_indexes = {idx['name'] for idx in self.collection.list_indexes()}
            print(f"现有索引: {existing_indexes}")

            # 定义需要创建的索引
            indexes_to_create = [
                # 唯一索引
                ("case_number", {"unique": True, "name": "case_number_unique"}),
                # 单字段索引
                ("court", {"name": "court_1"}),
                ("region", {"name": "region_1"}),
                ("case_type", {"name": "case_type_1"}),
                ("case_cause", {"name": "case_cause_1"}),
                (("judgment_date", DESCENDING), {"name": "judgment_date_-1"}),
                (("publish_date", DESCENDING), {"name": "publish_date_-1"}),
            ]

            # 复合索引
            compound_indexes = [
                ([("region", ASCENDING), ("court", ASCENDING)], {"name": "region_1_court_1"}),
                ([("case_type", ASCENDING), ("judgment_date", DESCENDING)], {"name": "case_type_1_judgment_date_-1"}),
                ([("court", ASCENDING), ("judgment_date", DESCENDING)], {"name": "court_1_judgment_date_-1"}),
            ]

            # 创建单字段索引
            for index_spec, options in indexes_to_create:
                index_name = options.get("name")
                if index_name not in existing_indexes:
                    try:
                        self.collection.create_index(index_spec, **options)
                        print(f"✓ 创建索引: {index_name}")
                    except Exception as e:
                        print(f"⚠️ 索引 {index_name} 创建失败: {e}")
                else:
                    print(f"⚠️ 索引 {index_name} 已存在，跳过")

            # 创建复合索引
            for index_spec, options in compound_indexes:
                index_name = options.get("name")
                if index_name not in existing_indexes:
                    try:
                        self.collection.create_index(index_spec, **options)
                        print(f"✓ 创建复合索引: {index_name}")
                    except Exception as e:
                        print(f"⚠️ 复合索引 {index_name} 创建失败: {e}")
                else:
                    print(f"⚠️ 复合索引 {index_name} 已存在，跳过")

            # 创建文本索引（如果不存在）
            text_index_name = "text_search"
            if text_index_name not in existing_indexes:
                try:
                    self.collection.create_index(
                        [("case_name", TEXT), ("parties", TEXT), ("case_cause", TEXT)],
                        name=text_index_name
                    )
                    print(f"✓ 创建文本索引: {text_index_name}")
                except Exception as e:
                    print(f"⚠️ 文本索引创建失败: {e}")
            else:
                print(f"⚠️ 文本索引 {text_index_name} 已存在，跳过")

            print("✓ 索引创建/检查完成")

            # 显示最终索引信息
            final_indexes = self.collection.list_indexes()
            print("\n当前索引列表:")
            for idx in final_indexes:
                print(f"  - {idx['name']}: {idx.get('key', {})}")

        except Exception as e:
            print(f"✗ 索引操作失败: {e}")
    
    def get_collection_stats(self):
        """获取集合统计信息"""
        try:
            stats = self.db.command("collStats", "documents")
            return {
                "document_count": stats.get("count", 0),
                "storage_size": stats.get("storageSize", 0),
                "index_count": stats.get("nindexes", 0),
                "total_index_size": stats.get("totalIndexSize", 0)
            }
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}
    
    def setup_database(self):
        """初始化数据库"""
        print("=== 初始化裁判文书数据库 ===")
        print(f"数据库: {self.db.name}")
        print(f"集合: {self.collection.name}")
        
        # 创建验证规则
        self.create_schema_validation()
        
        # 创建索引
        self.create_indexes()
        
        print("\n✓ 数据库初始化完成")

def main():
    """主函数"""
    db = JudgmentDocumentDB()
    db.setup_database()

if __name__ == "__main__":
    main()
