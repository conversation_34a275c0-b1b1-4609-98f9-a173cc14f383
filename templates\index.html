<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>裁判文书导入系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .stat-card h3 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .stat-card p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .progress-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 30px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 15px;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 15px;
            transition: width 0.5s ease;
            position: relative;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            color: #333;
            z-index: 1;
        }
        
        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }
        
        .status-item strong {
            color: #333;
            display: block;
            margin-bottom: 5px;
        }
        
        .log-section {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 4px;
        }
        
        .log-info { background: rgba(66, 153, 225, 0.1); }
        .log-success { background: rgba(72, 187, 120, 0.1); }
        .log-warning { background: rgba(237, 137, 54, 0.1); }
        .log-error { background: rgba(245, 101, 101, 0.1); }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .pulsing {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>裁判文书导入系统</h1>
            <p>2021-2024年裁判文书数据批量导入与监控平台</p>
        </div>
        
        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <button id="initDbBtn" class="btn btn-success">初始化数据库</button>
                <button id="startBtn" class="btn btn-primary">开始导入</button>
                <button id="stopBtn" class="btn btn-danger" disabled>停止导入</button>
            </div>
            
            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalRecords">0</h3>
                    <p>总记录数</p>
                </div>
                <div class="stat-card">
                    <h3 id="processedRecords">0</h3>
                    <p>已处理</p>
                </div>
                <div class="stat-card">
                    <h3 id="successRecords">0</h3>
                    <p>成功导入</p>
                </div>
                <div class="stat-card">
                    <h3 id="currentSpeed">0</h3>
                    <p>处理速度 (条/秒)</p>
                </div>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-section">
                <h3>导入进度</h3>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                    <div id="progressText" class="progress-text">0%</div>
                </div>
                
                <div class="status-info">
                    <div class="status-item">
                        <strong>当前状态</strong>
                        <span id="currentStatus">待开始</span>
                    </div>
                    <div class="status-item">
                        <strong>当前文件</strong>
                        <span id="currentFile">无</span>
                    </div>
                    <div class="status-item">
                        <strong>已处理文件</strong>
                        <span id="processedFiles">0 / 0</span>
                    </div>
                    <div class="status-item">
                        <strong>预计剩余时间</strong>
                        <span id="estimatedTime">计算中...</span>
                    </div>
                </div>
            </div>
            
            <!-- 图表 -->
            <div class="chart-container">
                <canvas id="progressChart" width="400" height="200"></canvas>
            </div>
            
            <!-- 日志 -->
            <div class="log-section">
                <h3 style="margin-bottom: 15px;">系统日志</h3>
                <div id="logContainer">
                    <div class="log-entry log-info">系统已启动，等待操作...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Socket.IO连接
        const socket = io();
        
        // 图表初始化
        const ctx = document.getElementById('progressChart').getContext('2d');
        const progressChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '处理速度 (条/秒)',
                    data: [],
                    borderColor: '#4facfe',
                    backgroundColor: 'rgba(79, 172, 254, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // DOM元素
        const elements = {
            initDbBtn: document.getElementById('initDbBtn'),
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            totalRecords: document.getElementById('totalRecords'),
            processedRecords: document.getElementById('processedRecords'),
            successRecords: document.getElementById('successRecords'),
            currentSpeed: document.getElementById('currentSpeed'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            currentStatus: document.getElementById('currentStatus'),
            currentFile: document.getElementById('currentFile'),
            processedFiles: document.getElementById('processedFiles'),
            estimatedTime: document.getElementById('estimatedTime'),
            logContainer: document.getElementById('logContainer')
        };
        
        // 事件监听
        elements.initDbBtn.addEventListener('click', initDatabase);
        elements.startBtn.addEventListener('click', startImport);
        elements.stopBtn.addEventListener('click', stopImport);
        
        // Socket事件
        socket.on('progress_update', updateProgress);
        
        // 函数定义
        function initDatabase() {
            addLog('正在初始化数据库...', 'info');
            fetch('/api/init_database', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addLog('数据库初始化成功', 'success');
                    } else {
                        addLog(`数据库初始化失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    addLog(`请求失败: ${error}`, 'error');
                });
        }
        
        function startImport() {
            addLog('正在启动导入...', 'info');
            fetch('/api/start_import', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addLog('导入已启动', 'success');
                        elements.startBtn.disabled = true;
                        elements.stopBtn.disabled = false;
                    } else {
                        addLog(`启动失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    addLog(`请求失败: ${error}`, 'error');
                });
        }
        
        function stopImport() {
            addLog('正在停止导入...', 'warning');
            fetch('/api/stop_import', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addLog('导入已停止', 'warning');
                        elements.startBtn.disabled = false;
                        elements.stopBtn.disabled = true;
                    } else {
                        addLog(`停止失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    addLog(`请求失败: ${error}`, 'error');
                });
        }
        
        function updateProgress(data) {
            // 更新统计数据
            elements.totalRecords.textContent = data.total_records.toLocaleString();
            elements.processedRecords.textContent = data.processed_records.toLocaleString();
            elements.successRecords.textContent = data.success_records.toLocaleString();
            elements.currentSpeed.textContent = data.current_speed.toFixed(1);
            
            // 更新进度条
            const percentage = data.progress_percentage || 0;
            elements.progressFill.style.width = `${percentage}%`;
            elements.progressText.textContent = `${percentage.toFixed(1)}%`;
            
            // 更新状态信息
            elements.currentStatus.textContent = data.status;
            elements.currentFile.textContent = data.current_file || '无';
            elements.processedFiles.textContent = `${data.processed_files} / ${data.total_files}`;
            elements.estimatedTime.textContent = data.estimated_remaining;
            
            // 更新图表
            const now = new Date().toLocaleTimeString();
            progressChart.data.labels.push(now);
            progressChart.data.datasets[0].data.push(data.current_speed);
            
            // 保持最近20个数据点
            if (progressChart.data.labels.length > 20) {
                progressChart.data.labels.shift();
                progressChart.data.datasets[0].data.shift();
            }
            
            progressChart.update('none');
            
            // 根据状态更新按钮
            if (data.status === '进行中') {
                elements.startBtn.disabled = true;
                elements.stopBtn.disabled = false;
                elements.progressFill.classList.add('pulsing');
            } else {
                elements.startBtn.disabled = false;
                elements.stopBtn.disabled = true;
                elements.progressFill.classList.remove('pulsing');
            }
        }
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            elements.logContainer.appendChild(logEntry);
            elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
            
            // 保持最近100条日志
            while (elements.logContainer.children.length > 100) {
                elements.logContainer.removeChild(elements.logContainer.firstChild);
            }
        }
        
        // 页面加载完成后获取初始状态
        window.addEventListener('load', function() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    updateProgress(data.import_status);
                    addLog('页面加载完成，已连接到服务器', 'success');
                })
                .catch(error => {
                    addLog(`获取状态失败: ${error}`, 'error');
                });
        });
    </script>
</body>
</html>
