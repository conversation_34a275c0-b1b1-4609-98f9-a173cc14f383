import pandas as pd
import os

# 分析裁判文书数据结构
def analyze_data():
    file_path = '裁判文书/2021年/2021年01月裁判文书数据.csv'
    
    try:
        # 读取少量数据进行分析
        df = pd.read_csv(file_path, nrows=10)
        
        print("=== 裁判文书数据结构分析 ===")
        print(f"总列数: {len(df.columns)}")
        print("\n列名列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
        print("\n数据类型:")
        print(df.dtypes)
        
        print("\n数据样例:")
        for col in df.columns:
            sample_value = df[col].iloc[0] if pd.notna(df[col].iloc[0]) else "NULL"
            print(f"{col}: {sample_value}")
            
        print("\n空值统计:")
        print(df.isnull().sum())
        
        # 检查数据量
        total_rows = len(pd.read_csv(file_path))
        print(f"\n该文件总行数: {total_rows}")
        
    except Exception as e:
        print(f"分析数据时出错: {e}")

if __name__ == "__main__":
    analyze_data()
