#!/usr/bin/env python3
"""
裁判文书导入系统启动脚本
"""
import os
import sys
import subprocess
import time
import webbrowser
from threading import Timer
from config import CONFIG, print_config
from database_design import JudgmentDocumentDB
from error_handler import global_error_handler

def check_dependencies():
    """检查依赖项"""
    print("检查系统依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = [
        'flask', 'flask_socketio', 'pymongo', 'pandas'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n缺少以下包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_mongodb():
    """检查MongoDB连接"""
    print("检查MongoDB连接...")
    
    try:
        db = JudgmentDocumentDB(
            CONFIG.database.connection_string,
            CONFIG.database.database_name
        )
        
        # 测试连接
        db.client.admin.command('ping')
        print("✅ MongoDB连接成功")
        return True
        
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        print("请确保MongoDB服务正在运行")
        return False

def check_data_files():
    """检查数据文件"""
    print("检查数据文件...")
    
    data_path = CONFIG.import_config.data_path
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return False
    
    csv_count = 0
    for year in CONFIG.import_config.supported_years:
        year_path = os.path.join(data_path, year)
        if os.path.exists(year_path):
            csv_files = [f for f in os.listdir(year_path) if f.endswith('.csv')]
            csv_count += len(csv_files)
            print(f"✅ {year}: {len(csv_files)} 个CSV文件")
        else:
            print(f"⚠️  {year}: 目录不存在")
    
    if csv_count == 0:
        print("❌ 未找到CSV数据文件")
        return False
    
    print(f"✅ 总共找到 {csv_count} 个CSV文件")
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        CONFIG.logging.log_dir,
        'templates',
        'static'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")

def install_dependencies():
    """安装依赖项"""
    print("正在安装依赖项...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ 依赖项安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖项安装失败: {e}")
        return False

def open_browser():
    """打开浏览器"""
    url = f"http://localhost:{CONFIG.web.port}"
    print(f"正在打开浏览器: {url}")
    webbrowser.open(url)

def main():
    """主函数"""
    print("=" * 60)
    print("🏛️  裁判文书导入系统")
    print("=" * 60)
    
    # 打印配置
    print_config()
    print()
    
    # 创建必要目录
    create_directories()
    
    # 检查依赖项
    if not check_dependencies():
        print("\n是否要自动安装依赖项? (y/n): ", end="")
        if input().lower() == 'y':
            if not install_dependencies():
                sys.exit(1)
        else:
            sys.exit(1)
    
    # 检查MongoDB
    if not check_mongodb():
        print("\n请确保MongoDB服务正在运行，然后重新启动系统")
        sys.exit(1)
    
    # 检查数据文件
    if not check_data_files():
        print(f"\n请确保数据文件存在于 {CONFIG.import_config.data_path} 目录中")
        print("数据文件应按以下结构组织:")
        for year in CONFIG.import_config.supported_years:
            print(f"  {CONFIG.import_config.data_path}/{year}/")
            print(f"    ├── {year}01月裁判文书数据.csv")
            print(f"    ├── {year}02月裁判文书数据.csv")
            print(f"    └── ...")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("✅ 系统检查完成，准备启动Web服务...")
    print("=" * 60)
    
    # 延迟打开浏览器
    Timer(3.0, open_browser).start()
    
    # 启动Web应用
    try:
        from web_app import app, socketio
        
        print(f"\n🚀 启动Web服务器...")
        print(f"📱 访问地址: http://localhost:{CONFIG.web.port}")
        print(f"📊 管理面板: http://localhost:{CONFIG.web.port}")
        print("\n按 Ctrl+C 停止服务器")
        print("-" * 60)
        
        socketio.run(
            app,
            host=CONFIG.web.host,
            port=CONFIG.web.port,
            debug=CONFIG.web.debug
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        global_error_handler.log_error("启动Web服务器失败", e)
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
